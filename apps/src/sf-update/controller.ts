import {
  <PERSON>,
  <PERSON>,
  Body,
  Param,
  HttpException,
  HttpStatus,
  BadRequestException,
  Req,
} from '@nestjs/common';
import { SfUpdateService } from './service';
import { Request } from 'express';

@Controller('apphero')
export class SfUpdateController {
  constructor(private readonly sfUpdateService: SfUpdateService) {}

  @Patch('/update/:objectType')
  async updateSfObject(
    @Param('objectType') objectType: string,
    @Body() payload: any,
    @Req() request: Request,
  ): Promise<any> {
    try {
      // Validate object type
      if (!objectType || typeof objectType !== 'string') {
        throw new BadRequestException('Invalid object type provided');
      }

      // Validate payload
      if (!payload || typeof payload !== 'object') {
        throw new BadRequestException('Invalid payload provided');
      }

      // Validate that payload has an ID field for the update
      if (!payload.Id && !payload.id) {
        throw new BadRequestException(
          'Payload must contain an Id field for update operation',
        );
      }

      return await this.sfUpdateService.updateSfObject(
        objectType,
        payload,
        request,
      );
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new HttpException(
        error.message || 'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
